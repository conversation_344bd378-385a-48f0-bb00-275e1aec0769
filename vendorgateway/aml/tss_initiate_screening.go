package aml

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"
)

type initiateScreeningReq struct {
	req        *amlVgPb.InitiateScreeningRequest
	tenantConf *config.TSSCloudTenant
	*SecureExchange
}

func (s *initiateScreeningReq) URL() string {
	return s.tenantConf.URL + "/customerinfo/as501"
}

func (s *initiateScreeningReq) HTTPMethod() string {
	return http.MethodPost
}

func (s *initiateScreeningReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", s.URL())
	req.Header.Add("ApiToken", s.tenantConf.APIToken)
	return req
}

func (s *initiateScreeningReq) GetResponse() vendorapi.Response {
	return &initiateScreeningRes{}
}

func (s *initiateScreeningReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (s *initiateScreeningReq) Marshal() ([]byte, error) {
	purposeCode, err := getPurposeCode(s.req.GetPurpose())
	if err != nil {
		return nil, errors.Wrap(err, "error getting purpose code")
	}
	customerData, err := s.convertCustomerData()
	if err != nil {
		return nil, errors.Wrap(err, "error converting customer data")
	}
	req := &tss.AS501Request{
		RequestId:        s.req.GetVendorRequestId(),
		SourceSystemName: s.tenantConf.Name,
		Purpose:          purposeCode,
		CustomerList:     []*tss.CustomerData{customerData},
	}
	reqJson, err := protojson.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling request to JSON")
	}
	return reqJson, nil
}

func getPurposeCode(purpose amlVgPb.Purpose) (string, error) {
	switch purpose {
	case amlVgPb.Purpose_PURPOSE_INITIAL_SCREENING:
		return "03", nil
	case amlVgPb.Purpose_PURPOSE_CONTINUOUS_SCREENING:
		return "04", nil
	default:
		return "", errors.Errorf("unsupported purpose type: %s", purpose.String())
	}
}

func (s *initiateScreeningReq) convertCustomerData() (*tss.CustomerData, error) {
	if strings.TrimSpace(s.req.GetUserId()) == "" {
		return nil, errors.New("user id is mandatory")
	}
	if s.req.GetCustomerDetails().GetName() == nil &&
		s.req.GetCustomerDetails().GetPhoneNumber().GetNationalNumber() == 0 &&
		s.req.GetCustomerDetails().GetEmail() == "" &&
		s.req.GetCustomerDetails().GetPanNumber() == "" &&
		s.req.GetCustomerDetails().GetPassportNumber() == "" &&
		s.req.GetCustomerDetails().GetDrivingLicenseNumber() == "" {
		return nil, errors.New("at least one of name, phone number, email, pan, passport or driving license is mandatory")
	}

	gender, err := getGenderCode(s.req.GetCustomerDetails().GetGender())
	if err != nil {
		return nil, errors.Wrap(err, "error converting gender to code")
	}
	product, err := getProductCode(s.req.GetProduct())
	if err != nil {
		return nil, errors.Wrap(err, "error converting product to code")
	}
	nationality, err := getNationalityCode(s.req.GetCustomerDetails().GetNationality())
	if err != nil {
		return nil, errors.Wrap(err, "error converting nationality to code")
	}
	dateOfBirth := ""
	if s.req.GetCustomerDetails().GetDateOfBirth() != nil {
		if !datetime.IsDateBeforeTodayInLoc(s.req.GetCustomerDetails().GetDateOfBirth(), datetime.IST) {
			return nil, errors.New("date of birth is not before today")
		}
		dateOfBirth = datetime.DateToString(s.req.GetCustomerDetails().GetDateOfBirth(), "02-Jan-2006", datetime.IST)
	}

	// Convert address lines
	permanentAddress := s.req.GetCustomerDetails().GetPermanentAddress()
	correspondenceAddress := s.req.GetCustomerDetails().GetCorrespondenceAddress()
	permanentAddressLines := permanentAddress.GetAddressLines()
	correspondenceAddressLines := correspondenceAddress.GetAddressLines()
	permanentAddressLine1 := ""
	permanentAddressLine2 := ""
	permanentAddressLine3 := ""
	if len(permanentAddressLines) > 0 {
		permanentAddressLine1 = permanentAddressLines[0]
		if len(permanentAddressLines) > 1 {
			permanentAddressLine2 = permanentAddressLines[1]
			if len(permanentAddressLines) > 2 {
				permanentAddressLine3 = permanentAddressLines[2]
			}
		}
	}
	correspondenceAddressLine1 := ""
	correspondenceAddressLine2 := ""
	correspondenceAddressLine3 := ""
	if len(correspondenceAddressLines) > 0 {
		correspondenceAddressLine1 = correspondenceAddressLines[0]
		if len(correspondenceAddressLines) > 1 {
			correspondenceAddressLine2 = correspondenceAddressLines[1]
			if len(correspondenceAddressLines) > 2 {
				correspondenceAddressLine3 = correspondenceAddressLines[2]
			}
		}
	}
	return &tss.CustomerData{
		SourceSystemName:         s.tenantConf.Name,
		SourceSystemCustomerCode: s.req.GetUserId(),
		UniqueIdentifier:         s.req.GetVendorRequestId(),
		Products:                 product,
		ConstitutionType:         "1", // Individual
		FirstName:                s.req.GetCustomerDetails().GetName().GetFirstName(),
		MiddleName:               s.req.GetCustomerDetails().GetName().GetMiddleName(),
		LastName:                 s.req.GetCustomerDetails().GetName().GetLastName(),
		FatherFirstName:          s.req.GetCustomerDetails().GetFatherName().GetFirstName(),
		FatherMiddleName:         s.req.GetCustomerDetails().GetFatherName().GetMiddleName(),
		FatherLastName:           s.req.GetCustomerDetails().GetFatherName().GetLastName(),
		MotherFirstName:          s.req.GetCustomerDetails().GetMotherName().GetFirstName(),
		MotherMiddleName:         s.req.GetCustomerDetails().GetMotherName().GetMiddleName(),
		MotherLastName:           s.req.GetCustomerDetails().GetMotherName().GetLastName(),
		Gender:                   gender,
		DateOfBirth:              dateOfBirth,
		PersonalEmail:            s.req.GetCustomerDetails().GetEmail(),
		PersonalMobileIsd:        strconv.Itoa(int(s.req.GetCustomerDetails().GetPhoneNumber().GetCountryCode())),
		PersonalMobileNumber:     strconv.FormatUint(s.req.GetCustomerDetails().GetPhoneNumber().GetNationalNumber(), 10),

		// Permanent address
		PermanentAddressCountry: "IND",
		PermanentAddressZipCode: permanentAddress.GetPostalCode(),
		PermanentAddressLine1:   permanentAddressLine1,
		PermanentAddressLine2:   permanentAddressLine2,
		PermanentAddressLine3:   permanentAddressLine3,
		PermanentAddressCity:    permanentAddress.GetLocality(),
		PermanentAddressState:   permanentAddress.GetAdministrativeArea(),

		// Correspondence address
		CorrespondenceAddressCountry: "IND",
		CorrespondenceAddressZipCode: correspondenceAddress.GetPostalCode(),
		CorrespondenceAddressLine1:   correspondenceAddressLine1,
		CorrespondenceAddressLine2:   correspondenceAddressLine2,
		CorrespondenceAddressLine3:   correspondenceAddressLine3,
		CorrespondenceAddressCity:    correspondenceAddress.GetLocality(),
		CorrespondenceAddressState:   correspondenceAddress.GetAdministrativeArea(),

		PassportNumber:       s.req.GetCustomerDetails().GetPassportNumber(),
		VoterIdNumber:        s.req.GetCustomerDetails().GetVoterId(),
		DrivingLicenseNumber: s.req.GetCustomerDetails().GetDrivingLicenseNumber(),
		Pan:                  s.req.GetCustomerDetails().GetPanNumber(),
		Nationalities:        strings.Join([]string{nationality}, ","),
	}, nil
}

func getGenderCode(gender common.Gender) (string, error) {
	switch gender {
	case common.Gender_MALE:
		return "01", nil
	case common.Gender_FEMALE:
		return "02", nil
	case common.Gender_TRANSGENDER:
		return "03", nil
	default:
		return "", errors.New(fmt.Sprintf("unsupported gender type: %s", gender.String()))
	}
}

func getProductCode(product amlVgPb.Product) (string, error) {
	switch product {
	case amlVgPb.Product_PRODUCT_MUTUAL_FUND:
		return "MF", nil
	case amlVgPb.Product_PRODUCT_LOAN:
		return "Loan", nil
	default:
		return "", errors.Errorf("unsupported product type: %s", product.String())
	}
}

func getNationalityCode(nationality common.Nationality) (string, error) {
	switch nationality {
	case common.Nationality_NATIONALITY_INDIAN:
		return "IND", nil
	default:
		return "", errors.Errorf("unsupported nationality type: %s", nationality.String())
	}
}

type initiateScreeningRes struct{}

func (s *initiateScreeningRes) UnmarshalV2(ctx context.Context, b []byte) (proto.Message, error) {
	res := &tss.AS501Response{}
	err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling response to TSS proto")
	}
	initScreeningRes, err := s.convertTSSResToVGRes(res)
	if err != nil {
		return nil, errors.Wrap(err, "error converting TSS response to VG proto")
	}
	return initScreeningRes, nil
}

func (s *initiateScreeningRes) Unmarshal(b []byte) (proto.Message, error) {
	return s.UnmarshalV2(context.Background(), b)
}

func (s *initiateScreeningRes) convertTSSResToVGRes(res *tss.AS501Response) (*amlVgPb.InitiateScreeningResponse, error) {
	if res.GetValidationCode() != "" {
		return &amlVgPb.InitiateScreeningResponse{
			Status:                rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("validation error: %s", res.GetValidationDescription())),
			RequestId:             res.GetRequestId(),
			ValidationCode:        res.GetValidationCode(),
			ValidationDescription: res.GetValidationDescription(),
		}, nil
	}
	overallStatus, err := convertOverallStatus(res.GetOverallStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error converting overall status")
	}
	var customerResponses []*amlVgPb.CustomerResponse
	for _, tssCustomerResponse := range res.GetCustomerResponse() {
		var customerResponse *amlVgPb.CustomerResponse
		customerResponse, err = s.convertCustomerResponse(tssCustomerResponse)
		if err != nil {
			return nil, errors.Wrap(err, "error converting customer response")
		}
		customerResponses = append(customerResponses, customerResponse)
	}
	return &amlVgPb.InitiateScreeningResponse{
		Status:                rpc.StatusOk(),
		RequestId:             res.GetRequestId(),
		OverallStatus:         overallStatus,
		ValidationCode:        res.GetValidationCode(),
		ValidationDescription: res.GetValidationDescription(),
		CustomerResponse:      customerResponses,
	}, nil
}

func convertOverallStatus(status string) (amlVgPb.OverallStatus, error) {
	switch status {
	case "AcceptedByTW":
		return amlVgPb.OverallStatus_OVERALL_STATUS_ACCEPTED_BY_TW, nil
	case "RejectedByTW":
		return amlVgPb.OverallStatus_OVERALL_STATUS_REJECTED_BY_TW, nil
	default:
		return 0, errors.Errorf("unsupported overall status: %s", status)
	}
}

func (s *initiateScreeningRes) convertCustomerResponse(tssCustomerResponse *tss.CustomerResponse) (*amlVgPb.CustomerResponse, error) {
	validationOutcome, err := convertValidationOutcome(tssCustomerResponse.GetValidationOutcome())
	if err != nil {
		return nil, errors.Wrap(err, "error converting validation outcome")
	}
	suggestedAction, err := convertSuggestedAction(tssCustomerResponse.GetSuggestedAction())
	if err != nil {
		return nil, errors.Wrap(err, "error converting suggested action")
	}
	var purposeResponses []*amlVgPb.PurposeResponse
	for _, tssPurposeResponse := range tssCustomerResponse.GetPurposeResponse() {
		var purposeResponse *amlVgPb.PurposeResponse
		purposeResponse, err = s.convertPurposeResponse(tssPurposeResponse)
		if err != nil {
			return nil, errors.Wrap(err, "error converting purpose response")
		}
		purposeResponses = append(purposeResponses, purposeResponse)
	}
	return &amlVgPb.CustomerResponse{
		SourceSystemCustomerCode: tssCustomerResponse.GetSourceSystemCustomerCode(),
		ValidationOutcome:        validationOutcome,
		SuggestedAction:          suggestedAction,
		PurposeResponse:          purposeResponses,
		ValidationCode:           tssCustomerResponse.GetValidationCode(),
		ValidationDescription:    tssCustomerResponse.GetValidationDescription(),
		ValidationFailureCount:   uint32(tssCustomerResponse.GetValidationFailureCount()),
	}, nil
}

func convertValidationOutcome(outcome string) (amlVgPb.ValidationOutcome, error) {
	switch outcome {
	case "Success":
		return amlVgPb.ValidationOutcome_VALIDATION_OUTCOME_SUCCESS, nil
	case "Failure":
		return amlVgPb.ValidationOutcome_VALIDATION_OUTCOME_FAILURE, nil
	default:
		return 0, errors.Errorf("unsupported validation outcome: %s", outcome)
	}
}

func convertSuggestedAction(action string) (amlVgPb.SuggestedAction, error) {
	switch action {
	case "Proceed":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_PROCEED, nil
	case "Review":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_REVIEW, nil
	case "Stop":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_STOP, nil
	case "":
		return amlVgPb.SuggestedAction_SUGGESTED_ACTION_UNSPECIFIED, nil
	default:
		return 0, errors.Errorf("unsupported suggested action: %s", action)
	}
}

func (s *initiateScreeningRes) convertPurposeResponse(tssPurposeResponse *tss.PurposeResponse) (*amlVgPb.PurposeResponse, error) {
	var screeningData *amlVgPb.ScreeningData
	if tssPurposeResponse.GetData() != nil {
		var hitResponses []*amlVgPb.HitResponse
		for _, rawHit := range tssPurposeResponse.GetData().GetHitResponse() {
			matchType, err := convertMatchType(rawHit.MatchType)
			if err != nil {
				return nil, errors.Wrap(err, "error converting match type")
			}
			hitResponse := &amlVgPb.HitResponse{
				Source:                      rawHit.Source,
				WatchlistSourceId:           rawHit.WatchlistSourceId,
				MatchType:                   matchType,
				Score:                       rawHit.Score,
				ConfirmedMatchingAttributes: rawHit.ConfirmedMatchingAttributes,
			}
			hitResponses = append(hitResponses, hitResponse)
		}
		screeningData = &amlVgPb.ScreeningData{
			HitsDetected: tssPurposeResponse.GetData().GetHitsDetected() == "Yes",
			HitsCount:    uint32(tssPurposeResponse.GetData().GetHitsCount()),
			ConfirmedHit: tssPurposeResponse.GetData().GetConfirmedHit() == "Yes",
			ReportData:   tssPurposeResponse.GetData().GetReportData(),
			CaseId:       tssPurposeResponse.GetData().GetCaseId(),
			CaseUrl:      tssPurposeResponse.GetData().GetCaseUrl(),
			ProfileCode:  tssPurposeResponse.GetData().GetProfileCode(),
			HitResponse:  hitResponses,
		}
	}
	return &amlVgPb.PurposeResponse{
		Purpose:                tssPurposeResponse.GetPurpose(),
		PurposeCode:            tssPurposeResponse.GetPurposeCode(),
		ValidationCode:         tssPurposeResponse.GetValidationCode(),
		ValidationDescription:  tssPurposeResponse.GetValidationDescription(),
		ValidationFailureCount: uint32(tssPurposeResponse.GetValidationFailureCount()),
		Data:                   screeningData,
	}, nil
}

func convertMatchType(matchType string) (amlVgPb.MatchType, error) {
	switch matchType {
	case "Confirm Hit", "Confirmed":
		return amlVgPb.MatchType_MATCH_TYPE_CONFIRMED, nil
	case "Probable":
		return amlVgPb.MatchType_MATCH_TYPE_PROBABLE, nil
	case "KeyActivity":
		return amlVgPb.MatchType_MATCH_TYPE_KEY_ACTIVITY, nil
	default:
		return 0, errors.Errorf("unsupported match type: %s", matchType)
	}
}

func (s *initiateScreeningRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}
