syntax = "proto3";

package vendorgateway.aml;

import "api/aml/data.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/document_proof.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/income_slab.proto";
import "api/typesv2/common/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/nationality.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/politically_exposed_status.proto";
import "api/vendorgateway/request_header.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aml";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aml";

// Aml service is used to communicate with AML vendors for screening our customers
service Aml {
  // RPC to check the user's details for any money laundering activities
  // by querying against the vendor's database of money launderers.
  rpc ScreenCustomer (ScreenCustomerRequest) returns (ScreenCustomerResponse);

  rpc InitiateScreening(InitiateScreeningRequest) returns (InitiateScreeningResponse);
}

message ScreenCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // unique for each actor
  string record_identifier = 2;
  // request id to be passed to vendor
  string vendor_request_id = 3;
  // deprecated since AML screening rules are configured based on AmlProduct and this is of no use
  .aml.AmlEntity entity = 4 [deprecated = true];
  .aml.AmlProduct product = 5;
  CustomerDetails customer_details = 6 [(validate.rules).message.required = true];

  // Owner of the screening request.
  // Determines the correct API URL and configuration needed for the screening request with the vendor.
  // The API URL and configuration may differ by owner to comply with regulations,
  // ensuring only the owner’s designated case reviewer can access and review potential matches on money launderer lists.
  api.typesv2.common.Owner owner = 7;
}

message ScreenCustomerResponse {
  rpc.Status status = 1;
  // rejection message if the request is rejected
  string rejection_message = 2;
  // rejection code if the request is rejected
  .aml.RejectionCode rejection_code = 3;
  // record identifier passed in the request
  string record_identifier = 4;
  MatchStatus match_status = 5;
  // case id if matched and case is generated
  string case_id = 6;
  // case link if matched and case is generated
  string case_link = 7;
  // count of matches found
  uint64 alert_count = 8;
  // details of all the matches
  repeated .aml.MatchDetails match_details = 9;
}

message CustomerDetails {
  // name of the customer - Mandatory
  api.typesv2.common.Name name = 1 [(validate.rules).message.required = true];
  // father name - NOT Mandatory
  api.typesv2.common.Name father_name = 2;
  // mother name - NOT Mandatory
  api.typesv2.common.Name mother_name = 3;
  // gender - NOT Mandatory
  api.typesv2.common.Gender gender = 4;
  // marital status - NOT Mandatory
  api.typesv2.common.MaritalStatus marital_status = 5;
  // income slab - NOT Mandatory
  api.typesv2.common.IncomeSlab income_slab = 6;
  // pan number - NOT Mandatory
  string pan_number = 7;
  // nationality - Mandatory
  api.typesv2.common.Nationality nationality = 8 [(validate.rules).enum = {not_in: [0]}];
  // passport id number - NOT Mandatory
  string passport_number = 9;
  // passport expiry date - Mandatory if passport provided
  google.type.Date passport_expiry_date = 10;
  // driving license id number - NOT Mandatory
  string driving_license_number = 11;
  // passport expiry date - Mandatory if driving license provided
  google.type.Date driving_license_expiry_date = 12;
  // voter id number - NOT Mandatory
  string voter_id = 13;
  // document type of proof of address provided - NOT Mandatory
  api.typesv2.common.DocumentProofType poa_type = 14;
  // phone number - NOT Mandatory
  api.typesv2.common.PhoneNumber phone_number = 15;
  // email - NOT Mandatory
  string email = 16;
  // date of birth - NOT Mandatory
  google.type.Date date_of_birth = 17;
  // permanent address - NOT Mandatory
  api.typesv2.common.PostalAddress permanent_address = 18;
  // correspondence address - NOT Mandatory
  api.typesv2.common.PostalAddress correspondence_address = 19;
  // politically exposed status - NOT Mandatory
  api.typesv2.common.PoliticallyExposedStatus politically_exposed_status = 20;
  // employment type - NOT Mandatory
  api.typesv2.common.EmploymentType employment_type = 21;
}

// represents whether a match is found or not after screening is done
enum MatchStatus {
  MATCH_STATUS_UNSPECIFIED = 0;
  // Match found in screening
  MATCH_STATUS_MATCHED = 1;
  // Match not found in screening
  MATCH_STATUS_NOT_MATCHED = 2;
  // Error from vendor in screening attempt
  MATCH_STATUS_ERROR = 3;
}

message InitiateScreeningRequest {
  vendorgateway.RequestHeader header = 1;

  api.typesv2.common.Owner owner = 2;

  // Unique identifier of a user in caller's system for referring to during case reviews
  string user_id = 3;

  // A unique request id to be passed to vendor
  string vendor_request_id = 4;

  Product product = 5;

  Purpose purpose = 6;

  // TODO: Revisit mandatory fields as per vendor requirements
  CustomerDetails customer_details = 7 [(validate.rules).message.required = true];
}

enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_MUTUAL_FUND = 1;
  PRODUCT_LOAN = 2;
}

enum Purpose {
  PURPOSE_UNSPECIFIED = 0;
  PURPOSE_INITIAL_SCREENING = 1;
  PURPOSE_CONTINUOUS_SCREENING = 2;
}

// Enum for overall status of the screening request
enum OverallStatus {
  OVERALL_STATUS_UNSPECIFIED = 0;
  // Request was accepted by TrackWizz
  OVERALL_STATUS_ACCEPTED_BY_TW = 1;
  // Request was rejected by TrackWizz
  OVERALL_STATUS_REJECTED_BY_TW = 2;
}

// Enum for validation outcome
enum ValidationOutcome {
  VALIDATION_OUTCOME_UNSPECIFIED = 0;
  // Validation was successful
  VALIDATION_OUTCOME_SUCCESS = 1;
  // Validation failed
  VALIDATION_OUTCOME_FAILURE = 2;
}

// Enum for suggested action based on screening results
enum SuggestedAction {
  SUGGESTED_ACTION_UNSPECIFIED = 0;
  // Proceed with the customer onboarding (no hits found)
  SUGGESTED_ACTION_PROCEED = 1;
  // Review the customer (probable hits found)
  SUGGESTED_ACTION_REVIEW = 2;
  // Stop the customer onboarding (confirmed hits found)
  SUGGESTED_ACTION_STOP = 3;
}

// Enum for match type in hit response
enum MatchType {
  MATCH_TYPE_UNSPECIFIED = 0;
  // Confirmed hit/match
  MATCH_TYPE_CONFIRMED = 1;
  // Probable hit/match
  MATCH_TYPE_PROBABLE = 2;
  // Key activity match
  MATCH_TYPE_KEY_ACTIVITY = 3;
}

// Represents a single hit/match found during screening
message HitResponse {
  // Source of the watchlist where the hit was found
  string source = 1;
  // TrackWizz ID for the watchlist record
  string watchlist_source_id = 2;
  // Type of match (Confirmed, Probable, etc.)
  MatchType match_type = 3;
  // Score assigned to the match
  double score = 4;
  // Attributes that confirmed the match
  string confirmed_matching_attributes = 5;
}

// Data specific to screening purpose
message ScreeningData {
  // Whether hits were detected (Yes/No)
  bool hits_detected = 1;
  // Total number of hits detected
  uint32 hits_count = 2;
  // Whether any confirmed hits were found
  bool confirmed_hit = 3;
  // Case ID generated if hits are found
  string case_id = 4;
  // Case URL for reviewing the hits
  string case_url = 5;
  // Base64 encoded report data
  string report_data = 6;
  // Profile code used for screening
  string profile_code = 7;
  // List of individual hits/matches
  repeated HitResponse hit_response = 8;
}

// Response for a specific purpose
message PurposeResponse {
  // Name/description of the purpose
  string purpose = 1;
  // Purpose code (01, 02, 03, 04, 05)
  string purpose_code = 2;
  // Validation code for this purpose
  string validation_code = 3;
  // Validation description for this purpose
  string validation_description = 4;
  // Number of validation failures for this purpose
  uint32 validation_failure_count = 5;
  // Screening data (only present for screening purposes)
  ScreeningData data = 6;
}

// Response for a customer
message CustomerResponse {
  // Source system customer code from the request
  string source_system_customer_code = 1;
  // Application reference number from the request
  string application_ref_number = 2;
  // Outcome of validation for this customer
  ValidationOutcome validation_outcome = 3;
  // Overall suggested action for this customer
  SuggestedAction suggested_action = 4;
  // Responses for each purpose requested
  repeated PurposeResponse purpose_response = 5;
  // Validation code for this customer
  string validation_code = 6;
  // Validation description for this customer
  string validation_description = 7;
  // Number of validation failures for this customer
  uint32 validation_failure_count = 8;
}

message InitiateScreeningResponse {
  rpc.Status status = 1;

  // Request ID from the original request
  string request_id = 2;
  // Overall status of the request
  OverallStatus overall_status = 3;
  // Main validation code (if any)
  string validation_code = 4;
  // Main validation description (if any)
  string validation_description = 5;
  // List of customer responses
  repeated CustomerResponse customer_response = 6;
  // Related person responses (currently not used but included for completeness)
  repeated CustomerResponse related_person_response = 7;
  // Related person relation responses (currently not used but included for completeness)
  repeated CustomerResponse related_person_relation_response = 8;
}
